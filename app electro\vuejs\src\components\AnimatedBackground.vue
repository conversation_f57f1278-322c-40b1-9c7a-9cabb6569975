<template>
  <div class="animated-background">
    <!-- Sky Background -->
    <div :class="[
      'absolute inset-0 transition-all duration-1000',
      isDarkMode 
        ? 'bg-gradient-to-b from-gray-900 via-gray-800 to-gray-700' 
        : 'bg-gradient-to-b from-blue-400 via-blue-300 to-green-200'
    ]"></div>

    <!-- Clouds -->
    <div class="absolute inset-0 overflow-hidden">
      <div 
        v-for="(cloud, index) in clouds" 
        :key="`cloud-${index}`"
        :class="[
          'absolute rounded-full opacity-80 animate-float-cloud',
          isDarkMode ? 'bg-gray-600' : 'bg-white'
        ]"
        :style="{
          width: cloud.size + 'px',
          height: cloud.size * 0.6 + 'px',
          top: cloud.y + '%',
          left: cloud.x + '%',
          animationDelay: cloud.delay + 's',
          animationDuration: cloud.duration + 's'
        }"
      >
        <!-- Cloud parts for more realistic shape -->
        <div 
          :class="[
            'absolute rounded-full',
            isDarkMode ? 'bg-gray-600' : 'bg-white'
          ]"
          :style="{
            width: cloud.size * 0.7 + 'px',
            height: cloud.size * 0.7 + 'px',
            top: '-20%',
            left: '10%'
          }"
        ></div>
        <div 
          :class="[
            'absolute rounded-full',
            isDarkMode ? 'bg-gray-600' : 'bg-white'
          ]"
          :style="{
            width: cloud.size * 0.5 + 'px',
            height: cloud.size * 0.5 + 'px',
            top: '-10%',
            right: '15%'
          }"
        ></div>
      </div>
    </div>

    <!-- Mountains/Hills -->
    <div class="absolute bottom-0 left-0 right-0 h-1/3">
      <svg viewBox="0 0 1200 400" class="w-full h-full">
        <path 
          :fill="isDarkMode ? '#374151' : '#10b981'"
          d="M0,400 L0,200 Q200,150 400,180 Q600,120 800,160 Q1000,100 1200,140 L1200,400 Z"
        />
        <path 
          :fill="isDarkMode ? '#4b5563' : '#059669'"
          d="M0,400 L0,250 Q300,200 600,230 Q900,180 1200,210 L1200,400 Z"
        />
      </svg>
    </div>

    <!-- Trees -->
    <div class="absolute bottom-0 left-0 right-0 h-1/4 overflow-hidden">
      <div 
        v-for="(tree, index) in trees" 
        :key="`tree-${index}`"
        class="absolute bottom-0 animate-sway"
        :style="{
          left: tree.x + '%',
          animationDelay: tree.delay + 's',
          animationDuration: tree.duration + 's'
        }"
      >
        <!-- Tree trunk -->
        <div 
          :class="[
            'absolute bottom-0 rounded-t-lg',
            isDarkMode ? 'bg-amber-900' : 'bg-amber-800'
          ]"
          :style="{
            width: tree.trunkWidth + 'px',
            height: tree.trunkHeight + 'px',
            left: '50%',
            transform: 'translateX(-50%)'
          }"
        ></div>
        <!-- Tree foliage -->
        <div 
          :class="[
            'absolute rounded-full',
            isDarkMode ? 'bg-green-800' : 'bg-green-600'
          ]"
          :style="{
            width: tree.foliageSize + 'px',
            height: tree.foliageSize + 'px',
            bottom: tree.trunkHeight * 0.7 + 'px',
            left: '50%',
            transform: 'translateX(-50%)'
          }"
        ></div>
      </div>
    </div>

    <!-- Road -->
    <div class="absolute bottom-0 left-0 right-0 h-16">
      <div :class="[
        'w-full h-full relative',
        isDarkMode ? 'bg-gray-800' : 'bg-gray-600'
      ]">
        <!-- Road markings -->
        <div class="absolute top-1/2 left-0 right-0 h-1 flex justify-center">
          <div 
            v-for="i in 20" 
            :key="`road-mark-${i}`"
            :class="[
              'h-full mx-8 animate-road-marking',
              isDarkMode ? 'bg-gray-400' : 'bg-yellow-300'
            ]"
            :style="{
              width: '40px',
              animationDelay: (i * 0.1) + 's'
            }"
          ></div>
        </div>
      </div>
    </div>

    <!-- Car -->
    <div class="absolute bottom-16 animate-drive-car">
      <div class="relative">
        <!-- Car body -->
        <div :class="[
          'w-20 h-8 rounded-lg relative',
          isDarkMode ? 'bg-blue-600' : 'bg-red-500'
        ]">
          <!-- Car windows -->
          <div :class="[
            'absolute top-1 left-2 right-2 h-4 rounded',
            isDarkMode ? 'bg-gray-300' : 'bg-blue-200'
          ]"></div>
          <!-- Car wheels -->
          <div :class="[
            'absolute -bottom-2 left-2 w-4 h-4 rounded-full animate-spin-wheel',
            isDarkMode ? 'bg-gray-900' : 'bg-gray-800'
          ]"></div>
          <div :class="[
            'absolute -bottom-2 right-2 w-4 h-4 rounded-full animate-spin-wheel',
            isDarkMode ? 'bg-gray-900' : 'bg-gray-800'
          ]"></div>
        </div>
      </div>
    </div>

    <!-- Birds -->
    <div class="absolute inset-0 overflow-hidden">
      <div 
        v-for="(bird, index) in birds" 
        :key="`bird-${index}`"
        class="absolute animate-fly-bird"
        :style="{
          top: bird.y + '%',
          left: bird.x + '%',
          animationDelay: bird.delay + 's',
          animationDuration: bird.duration + 's'
        }"
      >
        <div :class="[
          'w-2 h-1 transform rotate-12',
          isDarkMode ? 'bg-gray-800' : 'bg-gray-700'
        ]"></div>
        <div :class="[
          'w-2 h-1 transform -rotate-12 -mt-1',
          isDarkMode ? 'bg-gray-800' : 'bg-gray-700'
        ]"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  isDarkMode: boolean
}

defineProps<Props>()

// Cloud data
const clouds = ref([
  { x: 10, y: 10, size: 80, delay: 0, duration: 20 },
  { x: 30, y: 15, size: 60, delay: 2, duration: 25 },
  { x: 60, y: 8, size: 100, delay: 4, duration: 18 },
  { x: 80, y: 20, size: 70, delay: 6, duration: 22 },
  { x: -10, y: 12, size: 90, delay: 8, duration: 24 }
])

// Tree data
const trees = ref([
  { x: 15, trunkWidth: 8, trunkHeight: 30, foliageSize: 40, delay: 0, duration: 4 },
  { x: 25, trunkWidth: 6, trunkHeight: 25, foliageSize: 35, delay: 1, duration: 5 },
  { x: 45, trunkWidth: 10, trunkHeight: 35, foliageSize: 50, delay: 2, duration: 3.5 },
  { x: 65, trunkWidth: 7, trunkHeight: 28, foliageSize: 38, delay: 3, duration: 4.5 },
  { x: 85, trunkWidth: 9, trunkHeight: 32, foliageSize: 45, delay: 4, duration: 4 }
])

// Bird data
const birds = ref([
  { x: 20, y: 25, delay: 0, duration: 15 },
  { x: 40, y: 30, delay: 3, duration: 18 },
  { x: 70, y: 20, delay: 6, duration: 12 }
])

onMounted(() => {
  // Add some randomness to animations
  clouds.value.forEach(cloud => {
    cloud.delay += Math.random() * 2
  })
  
  trees.value.forEach(tree => {
    tree.delay += Math.random() * 1
  })
  
  birds.value.forEach(bird => {
    bird.delay += Math.random() * 3
  })
})
</script>

<style scoped>
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

@keyframes float-cloud {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
}

@keyframes sway {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(2deg); }
}

@keyframes drive-car {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(calc(100vw + 100px)); }
}

@keyframes spin-wheel {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes road-marking {
  0% { transform: translateX(-100px); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100px); opacity: 0; }
}

@keyframes fly-bird {
  0% { transform: translateX(-50px) translateY(0px); }
  25% { transform: translateX(25vw) translateY(-10px); }
  50% { transform: translateX(50vw) translateY(5px); }
  75% { transform: translateX(75vw) translateY(-5px); }
  100% { transform: translateX(calc(100vw + 50px)) translateY(0px); }
}

.animate-float-cloud {
  animation: float-cloud linear infinite;
}

.animate-sway {
  animation: sway ease-in-out infinite;
}

.animate-drive-car {
  animation: drive-car 15s linear infinite;
}

.animate-spin-wheel {
  animation: spin-wheel 0.5s linear infinite;
}

.animate-road-marking {
  animation: road-marking 2s linear infinite;
}

.animate-fly-bird {
  animation: fly-bird linear infinite;
}
</style>
